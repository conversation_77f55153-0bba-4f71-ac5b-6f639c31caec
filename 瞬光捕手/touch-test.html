<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>触摸支持测试 - 瞬光捕手</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-section h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-result {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: white;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .device-info {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: white;
        }
        
        .clear-btn {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .clear-btn:hover {
            background: rgba(244, 67, 54, 0.3);
        }
        
        @media (max-width: 768px) {
            .button-grid {
                grid-template-columns: 1fr;
            }
            
            .test-container {
                padding: 20px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: white; text-align: center; margin-bottom: 30px;">
            📱 触摸支持测试
        </h1>
        
        <!-- 设备信息 -->
        <div class="device-info">
            <h3>🔍 设备信息</h3>
            <div id="device-info"></div>
        </div>
        
        <!-- 主菜单按钮测试 -->
        <div class="test-section">
            <h3>🎮 主菜单按钮</h3>
            <div class="button-grid">
                <button class="menu-btn primary" data-test="start-game">开始游戏</button>
                <button class="menu-btn" data-test="level-editor">关卡编辑器</button>
                <button class="menu-btn" data-test="custom-levels">自定义关卡</button>
                <button class="menu-btn" data-test="leaderboard">排行榜</button>
                <button class="menu-btn" data-test="settings">设置</button>
            </div>
        </div>
        
        <!-- 小按钮测试 -->
        <div class="test-section">
            <h3>🔘 小按钮</h3>
            <div class="button-grid">
                <button class="small-btn" data-test="pause">暂停</button>
                <button class="small-btn" data-test="resume">继续</button>
                <button class="small-btn" data-test="restart">重新开始</button>
                <button class="small-btn" data-test="switch-player">切换玩家</button>
            </div>
        </div>
        
        <!-- 标签按钮测试 -->
        <div class="test-section">
            <h3>🏷️ 标签按钮</h3>
            <div class="button-grid">
                <button class="tab-btn active" data-test="global">全球排行</button>
                <button class="tab-btn" data-test="daily">今日排行</button>
                <button class="tab-btn" data-test="weekly">本周排行</button>
                <button class="tab-btn" data-test="perfect">完美击中</button>
            </div>
        </div>
        
        <!-- 工具按钮测试 -->
        <div class="test-section">
            <h3>🛠️ 工具按钮</h3>
            <div class="button-grid">
                <button class="tool-btn active" data-test="select">选择</button>
                <button class="tool-btn" data-test="spark">光点</button>
                <button class="tool-btn" data-test="obstacle">障碍</button>
                <button class="tool-btn" data-test="powerup">道具</button>
            </div>
        </div>
        
        <!-- 操作按钮测试 -->
        <div class="test-section">
            <h3>⚡ 操作按钮</h3>
            <div class="button-grid">
                <button class="action-btn" data-test="new">新建</button>
                <button class="action-btn" data-test="load">加载</button>
                <button class="action-btn" data-test="save">保存</button>
                <button class="action-btn" data-test="test">测试</button>
            </div>
        </div>
        
        <!-- 关卡操作按钮测试 -->
        <div class="test-section">
            <h3>🎯 关卡操作按钮</h3>
            <div class="button-grid">
                <button class="level-action-btn primary" data-test="play-level">开始游戏</button>
                <button class="level-action-btn" data-test="edit-level">编辑关卡</button>
                <button class="level-action-btn" data-test="info-level">查看详情</button>
            </div>
        </div>
        
        <!-- 测试结果 -->
        <div class="test-section">
            <h3>📊 测试结果</h3>
            <button class="clear-btn" onclick="clearResults()">清空结果</button>
            <div class="test-result" id="test-results">
                等待测试...
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/utils/touch-helper.js"></script>
    <script>
        let testResults = [];
        
        // 显示设备信息
        function showDeviceInfo() {
            const info = document.getElementById('device-info');
            const deviceType = TouchHelper.getTouchDeviceType();
            const isTouchDevice = TouchHelper.isTouchDevice();
            const userAgent = navigator.userAgent;
            const screenSize = `${window.screen.width}x${window.screen.height}`;
            const viewportSize = `${window.innerWidth}x${window.innerHeight}`;
            
            info.innerHTML = `
                <p><strong>设备类型:</strong> ${deviceType}</p>
                <p><strong>支持触摸:</strong> ${isTouchDevice ? '是' : '否'}</p>
                <p><strong>屏幕尺寸:</strong> ${screenSize}</p>
                <p><strong>视口尺寸:</strong> ${viewportSize}</p>
                <p><strong>用户代理:</strong> ${userAgent}</p>
            `;
        }
        
        // 记录测试结果
        function logResult(message) {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push(`[${timestamp}] ${message}`);
            
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = testResults.join('\n');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // 清空结果
        function clearResults() {
            testResults = [];
            document.getElementById('test-results').innerHTML = '等待测试...';
        }
        
        // 初始化测试
        function initTest() {
            showDeviceInfo();
            
            // 为所有测试按钮添加触摸支持
            const testButtons = document.querySelectorAll('[data-test]');
            
            testButtons.forEach(button => {
                const testName = button.dataset.test;
                const buttonText = button.textContent;
                
                if (window.touchHelper) {
                    window.touchHelper.addTouchFriendlyEvents(button, (e) => {
                        const eventType = e.type || 'unknown';
                        logResult(`✅ ${buttonText} (${testName}) - 事件类型: ${eventType}`);
                        
                        // 特殊处理标签按钮的激活状态
                        if (button.classList.contains('tab-btn')) {
                            document.querySelectorAll('.tab-btn').forEach(btn => {
                                btn.classList.remove('active');
                            });
                            button.classList.add('active');
                        }
                        
                        // 特殊处理工具按钮的激活状态
                        if (button.classList.contains('tool-btn')) {
                            document.querySelectorAll('.tool-btn').forEach(btn => {
                                btn.classList.remove('active');
                            });
                            button.classList.add('active');
                        }
                    });
                    
                    logResult(`🔧 为 "${buttonText}" 添加了触摸支持`);
                } else {
                    logResult(`❌ TouchHelper未加载，无法为 "${buttonText}" 添加触摸支持`);
                }
            });
            
            logResult(`📱 测试初始化完成，共 ${testButtons.length} 个按钮`);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initTest);
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            setTimeout(showDeviceInfo, 100);
        });
    </script>
</body>
</html>
